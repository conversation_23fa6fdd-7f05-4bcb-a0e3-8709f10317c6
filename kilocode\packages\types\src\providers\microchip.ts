import { z } from "zod"

import type { ModelInfo } from "../model.js"

// Microchip provider settings are defined in ../provider-settings.ts

/**
 * Available Microchip models (currently only one)
 * Always uses "microchip-chatbot-internal" model to match MPLAB AI implementation
 */
export const microchipModels = {
	"microchip-chatbot-internal": {
		maxTokens: 4096,
		contextWindow: 128000,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0.0, // Free for now
		outputPrice: 0.0, // Free for now
	},
} as const satisfies Record<string, ModelInfo>

export type MicrochipModelId = keyof typeof microchipModels
export const microchipDefaultModelId: MicrochipModelId = "microchip-chatbot-internal"
