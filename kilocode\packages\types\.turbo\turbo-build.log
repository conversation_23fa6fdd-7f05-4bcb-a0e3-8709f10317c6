
> @roo-code/types@0.0.0 build C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\packages\types
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.5.0
[34mCLI[39m Using tsup config: C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\packages\types\tsup.config.ts
[34mCLI[39m Target: es2022
[34mCJS[39m Build start
[34mESM[39m Build start
X [ERROR] Could not resolve "../base-provider-settings.js"

    src/providers/microchip.ts:3:43:
      3 │ ... { baseProviderSettingsSchema } from "../base-provider-settings.js"
        ╵                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

[31mCJS[39m [31mBuild failed[39m
[31mError: Build failed with 1 error:
src/providers/microchip.ts:3:43: ERROR: Could not resolve "../base-provider-settings.js"
    at failureErrorWithLog (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:1463:15)
    at C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:924:25
    at runOnEndCallbacks (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:1303:45)
    at buildResponseToResult (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:922:7)
    at C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:949:16
    at responseCallbacks.<computed> (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:601:9)
    at handleIncomingPacket (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:656:12)
    at Socket.readFromStdout (C:\Users\<USER>\My Files\AI Practice\open source\New folder - Copy\kilocode\node_modules\.pnpm\esbuild@0.25.5\node_modules\esbuild\lib\main.js:579:7)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)[39m
X [ERROR] Could not resolve "../base-provider-settings.js"

    src/providers/microchip.ts:3:43:
      3 │ ... { baseProviderSettingsSchema } from "../base-provider-settings.js"
        ╵                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

[31mESM[39m [31mBuild failed[39m
[34mDTS[39m Build start
src/provider-settings.ts(295,66): error TS2345: Argument of type 'string[]' is not assignable to parameter of type 'never'.

[31mDTS[39m [31mBuild error[39m
 ELIFECYCLE  Command failed with exit code 1.
