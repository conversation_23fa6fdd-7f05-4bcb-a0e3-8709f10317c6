import { Anthropic } from "@anthropic-ai/sdk"

import type { ModelInfo, microchipModels } from "@roo-code/types"

import type { ApiHandlerOptions } from "../../shared/api"
import { logger } from "../../utils/logging"

import { ApiStream } from "../transform/stream"

import { BaseProvider } from "./base-provider"
import type { SingleCompletionHandler, ApiHandlerCreateMessageMetadata } from "../index"

/**
 * Microchip API handler for interacting with the Microchip AI Chatbot service.
 *
 * This handler exactly matches the MPLAB AI coding assistant CHATBOTAPI.ts implementation:
 * - Always uses "microchip-chatbot-internal" model
 * - Uses CodeCompletionLLM endpoint
 * - Uses { questions: [message] } payload format
 * - Direct fetch API usage
 * - Enhanced error handling specific to Microchip API
 */
export class <PERSON>chipHandler extends BaseProvider implements SingleCompletionHandler {
	protected options: ApiHandlerOptions
	private apiUrl: string

	constructor(options: ApiHandlerOptions) {
		super()
		this.options = options

		// Use the provided base URL or default to Microchip's endpoint
		this.apiUrl = this.options.microchipBaseUrl ?? "https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM"

		logger.info("Initializing Microchip API handler", {
			ctx: "microchip",
			apiUrl: this.apiUrl,
		})
	}

	/**
	 * Creates a message stream from the Microchip API
	 *
	 * This implementation exactly matches the MPLAB AI coding assistant CHATBOTAPI.ts approach:
	 * - Uses { questions: [processedMessages] } payload format
	 * - Strips images from messages
	 * - Ensures prompts start with <|im_start|> prefix
	 *
	 * @param systemPrompt The system prompt to use
	 * @param messages The messages to send to the API
	 * @param metadata Optional metadata for the request
	 * @returns A stream of response chunks
	 */
	override async *createMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		metadata?: ApiHandlerCreateMessageMetadata,
	): ApiStream {
		const { id: model, info } = this.getModel()

		try {
			// Combine system prompt with the last user message like MPLAB AI
			const lastMessage = messages[messages.length - 1]
			let combinedMessage = systemPrompt

			if (lastMessage && lastMessage.role === "user") {
				if (typeof lastMessage.content === "string") {
					combinedMessage += "\n\n" + lastMessage.content
				} else if (Array.isArray(lastMessage.content)) {
					// Extract text content and strip images like MPLAB AI
					const textContent = lastMessage.content
						.filter((part) => part.type === "text")
						.map((part) => (part as any).text)
						.join("\n")
					combinedMessage += "\n\n" + textContent
				}
			}

			// Strip images and process the message like MPLAB AI
			const processedMessages = this.stripImages(combinedMessage)

			// Ensure prompt starts with <|im_start|> like MPLAB AI
			const processedPrompt = this.ensurePromptStartsWithImStart(processedMessages)

			// Create the payload using CHATBOTAPI format
			const conversation = {
				questions: [processedPrompt]
			}

			logger.info("Sending request to Microchip API", {
				ctx: "microchip",
				model,
				payload: conversation
			})

			// Make the request using fetch with custom headers
			const response = await fetch(this.apiUrl, {
				method: "POST",
				headers: this.getHeaders(),
				body: JSON.stringify(conversation)
			})

			if (!response.ok) {
				throw new Error(`Failed to connect to Microchip Chatbot 😔. HTTP error. Status: ${response.status}`)
			}

			const reader = response.body?.getReader()
			if (!reader) {
				throw new Error("Failed to read response from Microchip Chatbot 😕, please try again.")
			}

			// Stream the response like MPLAB AI
			let totalOutputTokens = 0
			while (true) {
				const { value, done } = await reader.read()
				if (value) {
					const chunk = new TextDecoder().decode(value)
					totalOutputTokens += chunk.length // Rough token estimation
					yield {
						type: "text",
						text: chunk
					}
				}
				if (done) {
					break
				}
			}

			// Add usage information
			yield {
				type: "usage",
				inputTokens: Math.ceil(processedPrompt.length / 4), // Rough estimation
				outputTokens: totalOutputTokens,
			}

		} catch (error: any) {
			// Handle the error like MPLAB AI
			logger.error("Microchip API error", { ctx: "microchip", error })
			throw new Error("There was an issue contacting the server 😓 Please try again later.")
		}
	}

	/**
	 * Complete a prompt using the Microchip API
	 * @param prompt The prompt to complete
	 * @returns The completion text
	 */
	async completePrompt(prompt: string): Promise<string> {
		const { id: modelId } = this.getModel()

		try {
			// Ensure prompt starts with <|im_start|> like MPLAB AI
			const processedPrompt = this.ensurePromptStartsWithImStart(prompt)

			// Create the payload using CHATBOTAPI format
			const conversation = {
				questions: [processedPrompt]
			}

			logger.info("Sending completePrompt request to Microchip API", {
				ctx: "microchip",
				model: modelId,
				payload: conversation
			})

			// Make the request using fetch
			const response = await fetch(this.apiUrl, {
				method: "POST",
				headers: this.getHeaders(),
				body: JSON.stringify(conversation)
			})

			if (!response.ok) {
				throw new Error(`Failed to connect to Microchip Chatbot 😔. HTTP error. Status: ${response.status}`)
			}

			const reader = response.body?.getReader()
			if (!reader) {
				throw new Error("Failed to read response from Microchip Chatbot 😕, please try again.")
			}

			// Read the complete response
			let completionText = ""
			while (true) {
				const { value, done } = await reader.read()
				if (value) {
					completionText += new TextDecoder().decode(value)
				}
				if (done) {
					break
				}
			}

			return completionText
		} catch (error: any) {
			// Handle the error like MPLAB AI
			logger.error("Microchip API error in completePrompt", { ctx: "microchip", error })
			throw new Error("There was an issue contacting the server 😓 Please try again later.")
		}
	}

	/**
	 * Gets the model information for the current configuration
	 * Always returns "microchip-chatbot-internal" to match MPLAB AI implementation
	 * @returns The model ID and info
	 */
	override getModel() {
		// Always use microchip-chatbot-internal model like MPLAB AI
		const modelId = "microchip-chatbot-internal"
		const info = microchipModels[modelId]

		return { id: modelId, info }
	}

	/**
	 * Get headers for the API request
	 * Uses "api-key" header like MPLAB AI implementation
	 * @returns Headers object
	 */
	private getHeaders(): Record<string, string> {
		return {
			"Content-Type": "application/json",
			"api-key": this.options.microchipApiKey ?? "",
		}
	}

	/**
	 * Strip images from message content like MPLAB AI
	 * @param content The content to process
	 * @returns Processed content with images stripped
	 */
	private stripImages(content: string): string {
		// For now, just return the content as-is since we're dealing with text
		// In a more complex implementation, this would remove image references
		return content
	}

	/**
	 * Ensure prompt starts with <|im_start|> prefix like MPLAB AI
	 * @param prompt The prompt to process
	 * @returns Processed prompt with proper prefix
	 */
	private ensurePromptStartsWithImStart(prompt: string): string {
		const prefix = '<|im_start|>user\n'
		if (!prompt.startsWith('<|im_start|>')) {
			return prefix + prompt
		}
		return prompt
	}
}
